<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Intro to HTML - Examination Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f4f4f4;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header>
            <h1>Introduction to HTML</h1>
            <p><strong>Examination Test Template</strong></p>
            <p>Student Name: _________________ | Date: _________________</p>
        </header>

        <!-- Main Content -->
        <main>
            <!-- Section 1: Basic HTML Structure -->
            <section>
                <h2>Section 1: HTML Document Structure</h2>
                <p>This template demonstrates the basic structure of an HTML document including:</p>
                <ul>
                    <li>DOCTYPE declaration</li>
                    <li>HTML element with language attribute</li>
                    <li>Head section with metadata</li>
                    <li>Body section with content</li>
                </ul>

                <div class="highlight">
                    <strong>Note:</strong> This is an example of a highlighted information box using CSS styling.
                </div>
            </section>

            <!-- Section 2: Text Formatting -->
            <section>
                <h2>Section 2: Text Formatting Elements</h2>
                <h3>Various Text Formatting Examples:</h3>
                <p>This paragraph contains <strong>bold text</strong>, <em>italic text</em>,
                <u>underlined text</u>, and <mark>highlighted text</mark>.</p>

                <p>You can also use <small>small text</small>, <del>deleted text</del>,
                <ins>inserted text</ins>, and text with <sup>superscript</sup> and <sub>subscript</sub>.</p>

                <blockquote>
                    "This is a blockquote element used for longer quotations."
                    <cite>- Citation Example</cite>
                </blockquote>
            </section>

            <!-- Section 3: Lists -->
            <section>
                <h2>Section 3: Lists</h2>

                <h3>Unordered List:</h3>
                <ul>
                    <li>First item</li>
                    <li>Second item</li>
                    <li>Third item with nested list:
                        <ul>
                            <li>Nested item 1</li>
                            <li>Nested item 2</li>
                        </ul>
                    </li>
                </ul>

                <h3>Ordered List:</h3>
                <ol>
                    <li>First step</li>
                    <li>Second step</li>
                    <li>Third step</li>
                </ol>

                <h3>Definition List:</h3>
                <dl>
                    <dt>HTML</dt>
                    <dd>HyperText Markup Language</dd>
                    <dt>CSS</dt>
                    <dd>Cascading Style Sheets</dd>
                </dl>
            </section>

            <!-- Section 4: Links and Images -->
            <section>
                <h2>Section 4: Links and Media</h2>

                <h3>Links:</h3>
                <p>Here are different types of links:</p>
                <ul>
                    <li><a href="https://www.example.com" target="_blank">External link (opens in new tab)</a></li>
                    <li><a href="#section5">Internal link to Section 5</a></li>
                    <li><a href="mailto:<EMAIL>">Email link</a></li>
                    <li><a href="tel:+1234567890">Phone link</a></li>
                </ul>

                <h3>Image Example:</h3>
                <img src="https://via.placeholder.com/300x200/007bff/ffffff?text=Sample+Image"
                     alt="Sample placeholder image"
                     width="300"
                     height="200">
                <p><em>Note: This is a placeholder image for demonstration purposes.</em></p>
            </section>

            <!-- Section 5: Tables -->
            <section id="section5">
                <h2>Section 5: Tables</h2>
                <table>
                    <caption>Sample Data Table</caption>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Age</th>
                            <th>City</th>
                            <th>Score</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>John Doe</td>
                            <td>25</td>
                            <td>New York</td>
                            <td>95</td>
                        </tr>
                        <tr>
                            <td>Jane Smith</td>
                            <td>30</td>
                            <td>Los Angeles</td>
                            <td>87</td>
                        </tr>
                        <tr>
                            <td>Bob Johnson</td>
                            <td>28</td>
                            <td>Chicago</td>
                            <td>92</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- Section 6: Forms -->
            <section>
                <h2>Section 6: Forms</h2>
                <div class="form-section">
                    <form action="#" method="post">
                        <h3>Sample Contact Form</h3>

                        <label for="name">Full Name:</label>
                        <input type="text" id="name" name="name" required>

                        <label for="email">Email Address:</label>
                        <input type="email" id="email" name="email" required>

                        <label for="phone">Phone Number:</label>
                        <input type="tel" id="phone" name="phone">

                        <label for="age">Age:</label>
                        <input type="number" id="age" name="age" min="1" max="120">

                        <label for="birthdate">Birth Date:</label>
                        <input type="date" id="birthdate" name="birthdate">

                        <label for="gender">Gender:</label>
                        <select id="gender" name="gender">
                            <option value="">Select...</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                            <option value="other">Other</option>
                        </select>

                        <label for="interests">Interests:</label>
                        <input type="checkbox" id="sports" name="interests" value="sports">
                        <label for="sports">Sports</label><br>
                        <input type="checkbox" id="music" name="interests" value="music">
                        <label for="music">Music</label><br>
                        <input type="checkbox" id="reading" name="interests" value="reading">
                        <label for="reading">Reading</label><br>

                        <label for="experience">Experience Level:</label>
                        <input type="radio" id="beginner" name="experience" value="beginner">
                        <label for="beginner">Beginner</label><br>
                        <input type="radio" id="intermediate" name="experience" value="intermediate">
                        <label for="intermediate">Intermediate</label><br>
                        <input type="radio" id="advanced" name="experience" value="advanced">
                        <label for="advanced">Advanced</label><br>

                        <label for="comments">Additional Comments:</label>
                        <textarea id="comments" name="comments" rows="4" placeholder="Enter your comments here..."></textarea>

                        <button type="submit">Submit Form</button>
                        <button type="reset">Reset Form</button>
                    </form>
                </div>
            </section>

            <!-- Section 7: Semantic HTML -->
            <section>
                <h2>Section 7: Semantic HTML Elements</h2>
                <p>This template uses semantic HTML5 elements such as:</p>
                <ul>
                    <li><code>&lt;header&gt;</code> - For the page header</li>
                    <li><code>&lt;main&gt;</code> - For the main content</li>
                    <li><code>&lt;section&gt;</code> - For content sections</li>
                    <li><code>&lt;article&gt;</code> - For standalone content</li>
                    <li><code>&lt;footer&gt;</code> - For the page footer</li>
                </ul>

                <article>
                    <h3>Sample Article</h3>
                    <p>This is an example of an article element. Articles are typically used for blog posts, news articles, or other standalone pieces of content.</p>
                    <time datetime="2024-01-15">Published on January 15, 2024</time>
                </article>
            </section>
        </main>

        <!-- Footer Section -->
        <footer>
            <p>&copy; 2024 Intro to HTML Course. This is a template for examination purposes.</p>
            <p>Last updated: <time datetime="2024-01-15">January 15, 2024</time></p>
        </footer>
    </div>
</body>
</html>